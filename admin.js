jQuery(document).ready(function($) {
    // Toggle schema fields visibility based on mode and enabled status
    $('#rife_schema_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#custom-jsonld-row').show();
            $('#auto-mode-info-row').show();
            $('#preview-row').show();
            $('#schema-status').removeClass('schema-status-disabled').addClass('schema-status-enabled');
            $('#status-text').text('Enabled');
        } else {
            $('#custom-jsonld-row').hide();
            $('#auto-mode-info-row').hide();
            $('#preview-row').hide();
            $('#schema-status').removeClass('schema-status-enabled').addClass('schema-status-disabled');
            $('#status-text').text('Disabled');
        }
    });
    
    // Auto mode no longer requires manual copying
    // Schema is automatically applied in auto mode

    // Mode switching functionality
    function switchMode() {
        const mode = $('input[name="rife_schema_input_mode"]:checked').val();
        
        if (mode === 'manual') {
            $('#manual-mode-row').show();
            $('#auto-mode-content').hide();
        } else if (mode === 'auto') {
            $('#manual-mode-row').hide();
            $('#auto-mode-content').show();
        }
    }
    
    // Initialize mode on page load
    switchMode();
    
    // Handle radio button change for mode switching
    $(document).on('change', 'input[name="rife_schema_input_mode"]', function() {
        switchMode();
    });
    
    // Handle mode tab clicks (if tabs exist)
    $('.rife-mode-tab').click(function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs
        $('.rife-mode-tab').removeClass('active');
        
        // Add active class to clicked tab
        $(this).addClass('active');
        
        // Update hidden input value
        const mode = $(this).data('mode');
        $('#rife_schema_input_mode').val(mode);
        
        // Switch mode display
        switchMode();
    });
    
    // Generate JSON-LD from form data
    $('#generate-jsonld').click(function() {
        var formData = {
            website_name: $('input[name="rife_schema_auto_website_name"]').val(),
            website_url: $('input[name="rife_schema_auto_website_url"]').val(),
            website_description: $('textarea[name="rife_schema_auto_website_description"]').val(),
            business_name: $('input[name="rife_schema_auto_business_name"]').val(),
            business_type: $('select[name="rife_schema_auto_business_type"]').val(),
            business_phone: $('input[name="rife_schema_auto_business_phone"]').val(),
            business_price_range: $('input[name="rife_schema_auto_business_price_range"]').val(),
            business_address: $('textarea[name="rife_schema_auto_business_address"]').val(),
            business_hours: $('textarea[name="rife_schema_auto_business_hours"]').val(),
            faq_data: $('textarea[name="rife_schema_auto_faq_data"]').val(),
            review_data: $('textarea[name="rife_schema_auto_review_data"]').val()
        };
        
        var jsonld = generateJsonLD(formData);
        $('#generated-jsonld-output').val(jsonld);
        $('#generated-jsonld-preview').show();
        
        // Also update the main JSON-LD field
        $('#rife_schema_global_jsonld').val(jsonld);
    });
    
    // Preview JSON-LD
    $('#preview-jsonld').click(function() {
        $('#generate-jsonld').click(); // Generate first
        
        var jsonld = $('#generated-jsonld-output').val();
        if (jsonld) {
            try {
                var parsed = JSON.parse(jsonld);
                alert('JSON-LD is valid and ready to use!');
            } catch (e) {
                alert('Error in JSON-LD: ' + e.message);
            }
        }
    });
    
    // Show auto-fill success message
    function showAutoFillSuccess(fieldName) {
        console.log('showAutoFillSuccess called for field:', fieldName);
        // Show success message
        const message = 'Auto fill berhasil untuk field: ' + fieldName;
        
        // Create or update success notification
        let notification = document.getElementById('rife-autofill-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'rife-autofill-notification';
            notification.style.cssText = `
                position: fixed;
                top: 32px;
                right: 20px;
                background: #00a32a;
                color: white;
                padding: 12px 20px;
                border-radius: 4px;
                z-index: 9999;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(notification);
            console.log('Notification element created');
        }
        
        notification.textContent = message;
        notification.style.display = 'block';
        console.log('Notification displayed:', message);
        
        // Auto hide after 3 seconds
        setTimeout(() => {
            if (notification) {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification && notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }

    // Auto-fill functionality
    function autoFillWebsiteName() {
        const field = document.querySelector('input[name="rife_schema_auto_website_name"]');
        if (field) {
            field.value = document.title.replace(' — WordPress', '').replace(' - WordPress', '');
            showAutoFillSuccess('website_name');
        }
    }

    function autoFillWebsiteUrl() {
        const field = document.querySelector('input[name="rife_schema_auto_website_url"]');
        if (field) {
            field.value = window.location.origin;
            showAutoFillSuccess('website_url');
        }
    }

    function autoFillWebsiteDescription() {
        console.log('autoFillWebsiteDescription called');
        const textarea = $('textarea[name="rife_schema_auto_website_description"]');
        console.log('Website description textarea found:', textarea.length);
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription && metaDescription.content) {
            textarea.val(metaDescription.content);
        } else {
            textarea.val('Kami menyediakan layanan profesional untuk pembuatan website berkualitas tinggi dengan desain modern dan fungsionalitas terbaik.');
        }
        showAutoFillSuccess('website_description');
    }

    function autoFillBusinessName() {
        console.log('autoFillBusinessName called');
        const input = $('input[name="rife_schema_auto_business_name"]');
        console.log('Business name input found:', input.length);
        input.val('PT. Contoh Bisnis Indonesia');
        showAutoFillSuccess('business_name');
    }

    function autoFillBusinessPhone() {
        console.log('autoFillBusinessPhone called');
        const input = $('input[name="rife_schema_auto_business_phone"]');
        console.log('Business phone input found:', input.length);
        input.val('+62-21-1234567');
        showAutoFillSuccess('business_phone');
    }

    function autoFillBusinessEmail() {
        console.log('autoFillBusinessEmail called');
        const input = $('input[name="rife_schema_auto_business_email"]');
        console.log('Business email input found:', input.length);
        input.val('<EMAIL>');
        showAutoFillSuccess('business_email');
    }

    function autoFillBusinessAddress() {
        console.log('autoFillBusinessAddress called');
        const textarea = $('textarea[name="rife_schema_auto_business_address"]');
        console.log('Business address textarea found:', textarea.length);
        const defaultAddress = {
            "streetAddress": "Jl. Sudirman No. 123",
            "addressLocality": "Jakarta Pusat",
            "postalCode": "10220",
            "addressCountry": "ID"
        };
        textarea.val(JSON.stringify(defaultAddress, null, 2));
        showAutoFillSuccess('business_address');
    }

    function autoFillBusinessHours() {
        console.log('autoFillBusinessHours called');
        const textarea = $('textarea[name="rife_schema_auto_business_hours"]');
        console.log('Business hours textarea found:', textarea.length);
        const defaultHours = {
            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            "opens": "09:00",
            "closes": "17:00"
        };
        textarea.val(JSON.stringify(defaultHours, null, 2));
        showAutoFillSuccess('business_hours');
    }

    function autoFillFaqData() {
        console.log('autoFillFaqData called');
        const textarea = $('textarea[name="rife_schema_auto_faq_data"]');
        console.log('FAQ data textarea found:', textarea.length);
        const defaultFaq = [
            {
                "question": "Berapa biaya pembuatan website?",
                "answer": "Biaya pembuatan website sangat terjangkau dan disesuaikan dengan kebutuhan Anda. Kami menawarkan paket mulai dari yang basic hingga premium dengan fitur lengkap."
            },
            {
                "question": "Berapa lama proses pembuatan website?",
                "answer": "Proses pembuatan website biasanya memakan waktu 1-4 minggu tergantung kompleksitas dan fitur yang diinginkan. Kami akan memberikan timeline yang jelas sejak awal."
            },
            {
                "question": "Apakah website yang dibuat responsive?",
                "answer": "Ya, semua website yang kami buat sudah responsive dan mobile-friendly. Website akan tampil sempurna di semua perangkat mulai dari desktop, tablet, hingga smartphone."
            }
        ];
        textarea.val(JSON.stringify(defaultFaq, null, 2));
        showAutoFillSuccess('faq_data');
    }

    function autoFillReviewData() {
        console.log('autoFillReviewData called');
        const textarea = $('textarea[name="rife_schema_auto_review_data"]');
        console.log('Review data textarea found:', textarea.length);
        const defaultReviews = [
            {
                "author": "Ahmad Rizki",
                "rating": 5,
                "reviewBody": "Pelayanan sangat memuaskan! Website yang dibuat sesuai dengan keinginan dan hasilnya sangat profesional. Tim sangat responsif dan membantu."
            },
            {
                "author": "Sari Dewi",
                "rating": 5,
                "reviewBody": "Kualitas website yang dibuat sangat bagus dan loading-nya cepat. Proses pembuatan juga tepat waktu sesuai jadwal yang disepakati."
            },
            {
                "author": "Budi Santoso",
                "rating": 4,
                "reviewBody": "Website yang dibuat sudah sesuai dengan kebutuhan bisnis kami. Design modern dan mudah digunakan. Recommended!"
            }
        ];
        textarea.val(JSON.stringify(defaultReviews, null, 2));
        showAutoFillSuccess('review_data');
    }

    // Auto-fill button event listeners
    $(document).on('click', '.rife-auto-fill-btn', function(e) {
        e.preventDefault();
        const field = $(this).attr('data-field');
        console.log('Auto fill button clicked for field:', field);
        
        switch(field) {
            case 'website_name':
                autoFillWebsiteName();
                break;
            case 'website_url':
                autoFillWebsiteUrl();
                break;
            case 'website_description':
                autoFillWebsiteDescription();
                break;
            case 'business_name':
                autoFillBusinessName();
                break;
            case 'business_phone':
                autoFillBusinessPhone();
                break;
            case 'business_email':
                autoFillBusinessEmail();
                break;
            case 'business_address':
                autoFillBusinessAddress();
                break;
            case 'business_hours':
                autoFillBusinessHours();
                break;
            case 'faq_data':
                autoFillFaqData();
                break;
            case 'review_data':
                autoFillReviewData();
                break;
            default:
                console.log('Unknown field:', field);
        }
    });

    // Function to generate JSON-LD from form data
    function generateJsonLD(data) {
        var schema = {
            "@context": "https://schema.org",
            "@graph": []
        };
        
        // Website Schema
        if (data.website_name && data.website_url) {
            var websiteSchema = {
                "@type": "WebSite",
                "url": data.website_url,
                "name": data.website_name,
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": data.website_url.replace(/\/$/, '') + "/?s={search_term_string}",
                    "query-input": "required name=search_term_string"
                }
            };
            schema["@graph"].push(websiteSchema);
        }
        
        // Business Schema
        if (data.business_name) {
            var businessSchema = {
                "@type": data.business_type || "ProfessionalService",
                "name": data.business_name,
                "image": "https://placehold.co/600x400/3B82F6/FFFFFF?text=" + encodeURIComponent(data.business_name)
            };
            
            if (data.website_url) {
                businessSchema["@id"] = data.website_url.replace(/\/$/, '') + "/#service";
                businessSchema["url"] = data.website_url;
            }
            
            if (data.business_phone) {
                businessSchema["telephone"] = data.business_phone;
            }
            
            if (data.business_price_range) {
                businessSchema["priceRange"] = data.business_price_range;
            }
            
            if (data.website_description) {
                businessSchema["description"] = data.website_description;
            }
            
            // Parse and add address
            if (data.business_address) {
                try {
                    var addressData = JSON.parse(data.business_address);
                    businessSchema["address"] = Object.assign({"@type": "PostalAddress"}, addressData);
                } catch (e) {
                    console.log('Invalid address JSON:', e);
                }
            }
            
            // Parse and add opening hours
            if (data.business_hours) {
                try {
                    var hoursData = JSON.parse(data.business_hours);
                    businessSchema["openingHoursSpecification"] = Object.assign({"@type": "OpeningHoursSpecification"}, hoursData);
                } catch (e) {
                    console.log('Invalid hours JSON:', e);
                }
            }
            
            schema["@graph"].push(businessSchema);
        }
        
        // FAQ Schema
        if (data.faq_data) {
            try {
                var faqItems = JSON.parse(data.faq_data);
                if (Array.isArray(faqItems) && faqItems.length > 0) {
                    var faqSchema = {
                        "@type": "FAQPage",
                        "mainEntity": []
                    };
                    
                    faqItems.forEach(function(faq) {
                        if (faq.question && faq.answer) {
                            faqSchema.mainEntity.push({
                                "@type": "Question",
                                "name": faq.question,
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": faq.answer
                                }
                            });
                        }
                    });
                    
                    if (faqSchema.mainEntity.length > 0) {
                        schema["@graph"].push(faqSchema);
                    }
                }
            } catch (e) {
                console.log('Invalid FAQ JSON:', e);
            }
        }
        
        // Review and Rating Schema
        if (data.review_data) {
            try {
                var reviewData = JSON.parse(data.review_data);
                
                // Handle array format (new format)
                if (Array.isArray(reviewData) && reviewData.length > 0) {
                    // Calculate aggregate rating
                    var totalRating = 0;
                    var validReviews = reviewData.filter(review => review.rating && review.author && review.reviewBody);
                    
                    if (validReviews.length > 0) {
                        validReviews.forEach(review => {
                            totalRating += parseFloat(review.rating);
                        });
                        
                        var avgRating = (totalRating / validReviews.length).toFixed(1);
                        
                        // Aggregate Rating Schema
                        var ratingSchema = {
                            "@type": "AggregateRating",
                            "itemReviewed": {
                                "@type": data.business_type || "ProfessionalService",
                                "name": data.business_name || "Professional Service"
                            },
                            "ratingValue": avgRating,
                            "bestRating": "5",
                            "worstRating": "1",
                            "ratingCount": validReviews.length.toString()
                        };
                        
                        if (data.website_url) {
                            ratingSchema.itemReviewed["@id"] = data.website_url.replace(/\/$/, '') + "/#service";
                        }
                        
                        schema["@graph"].push(ratingSchema);
                        
                        // Individual Reviews
                        validReviews.forEach(review => {
                            var reviewSchema = {
                                "@type": "Review",
                                "itemReviewed": {
                                    "@type": data.business_type || "ProfessionalService"
                                },
                                "author": {
                                    "@type": "Person",
                                    "name": review.author
                                },
                                "reviewRating": {
                                    "@type": "Rating",
                                    "ratingValue": review.rating.toString()
                                },
                                "reviewBody": review.reviewBody
                            };
                            
                            if (data.website_url) {
                                reviewSchema.itemReviewed["@id"] = data.website_url.replace(/\/$/, '') + "/#service";
                            }
                            
                            schema["@graph"].push(reviewSchema);
                        });
                    }
                }
                // Handle old object format for backward compatibility
                else if (reviewData.ratingValue || (reviewData.reviewAuthor && reviewData.reviewBody)) {
                    // Aggregate Rating
                    if (reviewData.ratingValue) {
                        var ratingSchema = {
                            "@type": "AggregateRating",
                            "itemReviewed": {
                                "@type": data.business_type || "ProfessionalService",
                                "name": data.business_name || "Professional Service"
                            },
                            "ratingValue": reviewData.ratingValue,
                            "bestRating": reviewData.bestRating || "5",
                            "worstRating": reviewData.worstRating || "1",
                            "ratingCount": reviewData.ratingCount || "1"
                        };
                        
                        schema["@graph"].push(ratingSchema);
                    }
                    
                    // Individual Review
                    if (reviewData.reviewAuthor && reviewData.reviewBody) {
                        var reviewSchema = {
                            "@type": "Review",
                            "itemReviewed": {
                                "@type": data.business_type || "ProfessionalService"
                            },
                            "author": {
                                "@type": "Person",
                                "name": reviewData.reviewAuthor
                            },
                            "reviewRating": {
                                "@type": "Rating",
                                "ratingValue": reviewData.reviewRating || "5"
                            },
                            "reviewBody": reviewData.reviewBody
                        };
                        
                        if (data.website_url) {
                            reviewSchema.itemReviewed["@id"] = data.website_url.replace(/\/$/, '') + "/#service";
                        }
                        
                        schema["@graph"].push(reviewSchema);
                    }
                }
            } catch (e) {
                console.log('Invalid review JSON:', e);
            }
        }
        
        return JSON.stringify(schema, null, 2);
    }
});