/* Rife Schema Markup Admin Styles */

/* Input Mode Radio Button Styling */
.rife-schema-mode-radio-group {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.rife-radio-option {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 25px;
    background: #ffffff;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 180px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.rife-radio-option:hover {
    border-color: #0073aa;
    background: #f8fbff;
    box-shadow: 0 4px 12px rgba(0, 115, 170, 0.15);
    transform: translateY(-2px);
}

.rife-radio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.rife-radio-option input[type="radio"]:checked + .rife-radio-label::before {
    background: #0073aa;
    border-color: #0073aa;
}

.rife-radio-option input[type="radio"]:checked + .rife-radio-label::after {
    opacity: 1;
    transform: scale(1);
}

.rife-radio-option:has(input:checked) {
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    border-color: #005a87;
    color: white;
    box-shadow: 0 6px 20px rgba(0, 115, 170, 0.3);
}

.rife-radio-option:has(input:checked) .rife-radio-label {
    color: white;
    font-weight: 600;
}

.rife-radio-option:has(input:checked) .rife-radio-description {
    color: rgba(255, 255, 255, 0.9);
}

.rife-radio-label {
    position: relative;
    font-size: 16px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
    padding-left: 35px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.rife-radio-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e0;
    border-radius: 50%;
    background: white;
    transition: all 0.3s ease;
}

.rife-radio-label::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 50%;
    transform: translateY(-50%) scale(0);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rife-radio-description {
    font-size: 13px;
    color: #64748b;
    text-align: center;
    line-height: 1.4;
    transition: all 0.3s ease;
}

/* Icon indicators for each mode */
.rife-radio-option[data-mode="manual"] .rife-radio-label::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23cbd5e0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px;
}

.rife-radio-option[data-mode="auto"] .rife-radio-label::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23cbd5e0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5z"></path><path d="M2 17l10 5 10-5"></path><path d="M2 12l10 5 10-5"></path></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px;
}

.rife-radio-option:has(input:checked)[data-mode="manual"] .rife-radio-label::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline></svg>');
}

.rife-radio-option:has(input:checked)[data-mode="auto"] .rife-radio-label::before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5z"></path><path d="M2 17l10 5 10-5"></path><path d="M2 12l10 5 10-5"></path></svg>');
}

/* Responsive design for mobile */
@media (max-width: 768px) {
    .rife-schema-mode-radio-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .rife-radio-option {
        min-width: auto;
        width: 100%;
    }
}

.rife-mode-toggle {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.rife-mode-toggle h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.rife-mode-toggle label {
    display: inline-block;
    margin-right: 20px;
    padding: 8px 15px;
    background: #fff;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rife-mode-toggle label:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.rife-mode-toggle input[type="radio"] {
    margin-right: 8px;
}

.rife-mode-toggle input[type="radio"]:checked + span {
    font-weight: bold;
    color: #0073aa;
}

.rife-mode-toggle label:has(input:checked) {
    background: #0073aa;
    color: white;
    border-color: #005a87;
}

.manual-mode-content,
.auto-mode-content {
    margin-top: 20px;
}

.rife-auto-form {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-top: 15px;
}

.rife-auto-form h3 {
    margin-top: 30px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #0073aa;
    color: #0073aa;
    font-size: 18px;
}

.rife-auto-form h3:first-child {
    margin-top: 0;
}

.rife-auto-form .form-table {
    margin-bottom: 25px;
}

.rife-auto-form .form-table th {
    width: 200px;
    font-weight: 600;
    color: #333;
}

.rife-auto-form .form-table td {
    padding-left: 20px;
}

.rife-auto-form input[type="text"],
.rife-auto-form input[type="url"],
.rife-auto-form input[type="tel"],
.rife-auto-form select,
.rife-auto-form textarea {
    width: 100%;
    max-width: 500px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.rife-auto-form input[type="text"]:focus,
.rife-auto-form input[type="url"]:focus,
.rife-auto-form input[type="tel"]:focus,
.rife-auto-form select:focus,
.rife-auto-form textarea:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 115, 170, 0.3);
}

.rife-auto-form textarea {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.auto-mode-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    text-align: center;
}

.auto-mode-actions .button {
    margin: 0 10px;
    padding: 10px 20px;
    font-size: 14px;
    min-width: 150px;
}

#generate-jsonld {
    background: #0073aa;
    border-color: #005a87;
    color: white;
}

#generate-jsonld:hover {
    background: #005a87;
}

#preview-jsonld {
    background: #00a32a;
    border-color: #007a1f;
    color: white;
}

#preview-jsonld:hover {
    background: #007a1f;
}

#generated-jsonld-preview {
    margin-top: 25px;
    padding: 20px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
}

#generated-jsonld-preview h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

#generated-jsonld-output {
    width: 100%;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 15px;
    resize: vertical;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .rife-mode-toggle label {
        display: block;
        margin-bottom: 10px;
        margin-right: 0;
        text-align: center;
    }
    
    .rife-auto-form .form-table th,
    .rife-auto-form .form-table td {
        display: block;
        width: 100%;
        padding: 5px 0;
    }
    
    .rife-auto-form .form-table td {
        padding-left: 0;
    }
    
    .auto-mode-actions .button {
        display: block;
        margin: 10px 0;
        width: 100%;
    }
}

/* Auto Fill Button Styles */
.rife-auto-fill-container {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.rife-auto-fill-btn {
    background: #0073aa;
    color: white;
    border: 1px solid #005a87;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.rife-auto-fill-btn:hover {
    background: #005a87;
    border-color: #004a6b;
    transform: translateY(-1px);
}

.rife-auto-fill-btn:active {
    transform: translateY(0);
}

.rife-auto-fill-hint {
    font-size: 11px;
    color: #666;
    font-style: italic;
}

/* JSON syntax highlighting hints */
.rife-auto-form textarea[placeholder*="JSON"] {
    background: #f8f8f8;
    border-left: 4px solid #0073aa;
}

/* Success/Error states */
.rife-success {
    border-color: #00a32a !important;
    background: #f0fff0;
}

.rife-error {
    border-color: #d63638 !important;
    background: #fff0f0;
}

/* Loading state */
.rife-loading {
    opacity: 0.6;
    pointer-events: none;
}

.rife-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status indicators */
.status-success {
    color: #00a32a;
    font-weight: bold;
}

.status-error {
    color: #d63638;
    font-weight: bold;
}

.status-loading {
    color: #0073aa;
    font-weight: bold;
}

/* Auto-generated schema preview */
.auto-schema-preview {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
}

.auto-schema-preview .description {
    margin-bottom: 10px;
    color: #0073aa;
    font-weight: 500;
}

.auto-generated-schema {
    background: #f6f7f7;
    border: 1px solid #ddd;
    font-family: 'Courier New', Courier, monospace;
    font-size: 12px;
    line-height: 1.4;
    resize: vertical;
}

.auto-schema-actions {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#use-auto-schema {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    transition: all 0.2s ease;
}

#use-auto-schema:hover {
    background: #005a87;
    border-color: #005a87;
}

.auto-schema-actions .description {
    margin: 0;
    font-style: italic;
    color: #666;
}

/* Auto Mode Status Styles */
.auto-mode-status {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #00a32a;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    position: relative;
    overflow: hidden;
}

.auto-mode-status::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #00a32a;
}

.auto-mode-status .status-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: #00a32a;
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
    vertical-align: middle;
}

.auto-mode-status .status-icon::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.auto-mode-status .status-title {
    font-weight: 600;
    color: #00a32a;
    font-size: 14px;
    margin-bottom: 5px;
    display: inline-block;
    vertical-align: middle;
}

.auto-mode-status .status-description {
    color: #2c5530;
    font-size: 13px;
    line-height: 1.4;
    margin: 8px 0 0 30px;
}

.auto-mode-status .status-features {
    margin: 10px 0 0 30px;
    font-size: 12px;
    color: #2c5530;
}

.auto-mode-status .status-features ul {
    margin: 5px 0;
    padding-left: 15px;
}

.auto-mode-status .status-features li {
    margin: 3px 0;
    list-style-type: disc;
}

/* Schema Preview in Auto Mode */
.auto-schema-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.auto-schema-display .preview-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    font-size: 13px;
}

.auto-schema-display .schema-content {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 11px;
    line-height: 1.5;
    color: #495057;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

/* Responsive adjustments for auto mode */
@media (max-width: 768px) {
    .auto-mode-status {
        padding: 12px;
    }
    
    .auto-mode-status .status-description,
    .auto-mode-status .status-features {
        margin-left: 0;
    }
    
    .auto-schema-display {
        padding: 12px;
    }
    
    .auto-schema-display .schema-content {
        font-size: 10px;
        max-height: 150px;
    }
}