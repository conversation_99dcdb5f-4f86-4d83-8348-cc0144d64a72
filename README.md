# Rife Schema Markup Plugin

Plugin WordPress untuk menambahkan Schema Markup JSON-LD yang kompatibel dengan Google Rich Results Test. Plugin ini memungkinkan Anda untuk dengan mudah menambahkan structured data ke website WordPress Anda untuk meningkatkan SEO dan visibilitas di hasil pencarian Google.

## 📋 Deskripsi

Rife Schema Markup adalah plugin WordPress yang dirancang khusus untuk menambahkan Schema Markup dalam format JSON-LD ke website Anda. Plugin ini mendukung berbagai jenis schema yang diakui oleh Google dan mesin pencari lainnya, membantu meningkatkan tampilan website Anda di hasil pencarian dengan rich snippets.

### ✨ Fitur Utama

- **Dual Mode Input**: Mode Manual (JSON-LD kustom) dan Mode Otomatis (form-based)
- **Flexible Output Location**: Pilihan penempatan di `<head>` atau footer
- **Individual Post/Page Control**: Meta box untuk mengatur schema per halaman
- **Auto-fill Indonesian Data**: Template data siap pakai dengan contoh Indonesia
- **Real-time Preview**: Preview JSON-LD yang dihasilkan sebelum dipublikasi
- **JSON Validation**: Validasi otomatis untuk memastikan format JSON yang benar
- **Google Rich Results Compatible**: Dioptimalkan untuk Google Rich Results Test

### 🎯 Schema Types yang Didukung

- **WebSite Schema**: Dengan SearchAction untuk kotak pencarian
- **Business/ProfessionalService**: Informasi bisnis lengkap dengan alamat dan jam operasional
- **Review & AggregateRating**: Sistem review dan rating agregat
- **FAQ Schema**: *(Dinonaktifkan karena pembatasan Google untuk situs non-authoritative)*

## 🚀 Instalasi

### Metode 1: Upload Manual

1. Download plugin dari repository ini
2. Upload folder `rife-schemamarkup` ke direktori `/wp-content/plugins/`
3. Aktifkan plugin melalui menu 'Plugins' di WordPress admin
4. Akses pengaturan melalui menu 'Schema Markup' di admin dashboard

### Metode 2: WordPress Admin

1. Login ke WordPress admin
2. Pergi ke Plugins → Add New
3. Upload file ZIP plugin
4. Klik 'Install Now' dan kemudian 'Activate'

## ⚙️ Konfigurasi

### Pengaturan Global

1. **Output Location**: Pilih apakah schema markup akan ditempatkan di `<head>` (recommended) atau footer
2. **Input Mode**: 
   - **Manual**: Input JSON-LD secara langsung
   - **Auto**: Gunakan form yang disediakan

### Mode Otomatis (Recommended)

Mode otomatis menyediakan form yang user-friendly untuk mengisi data schema:

#### Website Information
- **Website Name**: Nama website Anda
- **Website URL**: URL lengkap website
- **Website Description**: Deskripsi singkat website

#### Business Information
- **Business Name**: Nama bisnis/perusahaan
- **Business Type**: Jenis bisnis (default: ProfessionalService)
- **Phone**: Nomor telepon bisnis
- **Email**: Email bisnis
- **Price Range**: Rentang harga layanan (contoh: "Rp 500.000 - Rp 5.000.000")

#### Address (Format JSON)
```json
{
  "streetAddress": "Jl. Sudirman No. 123",
  "addressLocality": "Jakarta Pusat",
  "postalCode": "10220",
  "addressCountry": "ID"
}
```

#### Business Hours (Format JSON)
```json
{
  "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  "opens": "09:00",
  "closes": "17:00"
}
```

#### Review Data (Format JSON)
```json
[
  {
    "author": "Ahmad Rizki",
    "rating": 5,
    "reviewBody": "Pelayanan sangat memuaskan! Website yang dibuat sesuai dengan keinginan dan hasilnya sangat profesional."
  },
  {
    "author": "Sari Dewi",
    "rating": 5,
    "reviewBody": "Kualitas website yang dibuat sangat bagus dan loading-nya cepat."
  }
]
```

## 📖 Penggunaan

### 1. Pengaturan Global

1. Pergi ke **Schema Markup** di menu admin WordPress
2. Pilih **Output Location** (Head/Footer)
3. Pilih **Input Mode** (Manual/Auto)
4. Isi form sesuai mode yang dipilih
5. Klik **Save Settings**

### 2. Pengaturan Per Halaman/Post

Setiap halaman dan post memiliki meta box "Schema Markup" yang memungkinkan:

- **Enable Schema**: Aktifkan/nonaktifkan schema untuk halaman tertentu
- **Custom JSON-LD**: Override schema global dengan JSON-LD kustom
- **Auto Preview**: Lihat preview schema yang akan dihasilkan

### 3. Auto-fill Features

Plugin menyediakan tombol auto-fill untuk mengisi data dengan template Indonesia:

- **Website Data**: Nama, URL, dan deskripsi website
- **Business Data**: Informasi bisnis lengkap
- **Address**: Template alamat Indonesia
- **Business Hours**: Jam operasional standar
- **Reviews**: Contoh review dalam bahasa Indonesia

### 4. Preview dan Validasi

- Gunakan tombol **Preview JSON-LD** untuk melihat output
- Plugin akan memvalidasi format JSON secara otomatis
- Error akan ditampilkan jika format tidak valid

## 📁 Struktur File

```
rife-schemamarkup/
├── rife-schemamarkup.php    # File utama plugin (1,101 baris)
├── admin.css                # Styling admin interface (400+ baris)
├── admin.js                 # JavaScript functionality (531 baris)
└── README.md               # Dokumentasi ini
```

### File Descriptions

- **rife-schemamarkup.php**: Core plugin file yang berisi semua logika backend, hooks WordPress, dan fungsi-fungsi utama
- **admin.css**: Stylesheet untuk interface admin dengan desain modern dan responsive
- **admin.js**: JavaScript untuk interaktivitas admin, auto-fill, preview, dan validasi

## 🛠️ Teknologi

- **WordPress Plugin API**: Hooks, filters, dan WordPress standards
- **JSON-LD Format**: Format structured data yang direkomendasikan Google
- **jQuery**: Library JavaScript untuk admin interface
- **CSS3**: Modern styling dengan flexbox dan responsive design
- **PHP 7.4+**: Kompatibel dengan versi PHP modern
- **WordPress 5.0+**: Mendukung WordPress versi terbaru

## 🔧 Troubleshooting

### Schema Tidak Muncul di Google Rich Results Test

1. **Pastikan plugin aktif**: Cek di Plugins → Installed Plugins
2. **Periksa output location**: Pastikan menggunakan "Head" untuk hasil terbaik
3. **Validasi JSON**: Gunakan preview untuk memastikan JSON valid
4. **Clear cache**: Hapus cache website jika menggunakan caching plugin
5. **Periksa tema**: Pastikan tema memanggil `wp_head()` dengan benar

### JSON-LD Tidak Valid

1. **Gunakan auto-fill**: Mulai dengan template yang disediakan
2. **Validasi format**: Pastikan JSON menggunakan format yang benar
3. **Escape characters**: Perhatikan penggunaan tanda kutip dalam teks
4. **Preview first**: Selalu gunakan preview sebelum menyimpan

### Plugin Conflict

1. **Deactivate other SEO plugins**: Nonaktifkan plugin SEO lain sementara
2. **Check theme compatibility**: Pastikan tema mendukung wp_head()
3. **Debug mode**: Aktifkan WP_DEBUG untuk melihat error

### Performance Issues

1. **Use caching**: Plugin kompatibel dengan caching plugins
2. **Optimize JSON**: Hindari JSON yang terlalu besar
3. **Selective activation**: Aktifkan schema hanya di halaman yang diperlukan

## 🧪 Testing

### Google Rich Results Test

1. Buka [Google Rich Results Test](https://search.google.com/test/rich-results)
2. Masukkan URL halaman website Anda
3. Periksa apakah schema terdeteksi dengan benar
4. Perbaiki error yang muncul jika ada

### Manual Testing

1. **View Source**: Periksa source code halaman untuk melihat JSON-LD
2. **Browser Console**: Cek console untuk error JavaScript
3. **JSON Validator**: Gunakan online JSON validator untuk memvalidasi output

## 🤝 Kontribusi

### Cara Berkontribusi

1. **Fork** repository ini
2. **Create branch** untuk fitur baru (`git checkout -b feature/AmazingFeature`)
3. **Commit** perubahan Anda (`git commit -m 'Add some AmazingFeature'`)
4. **Push** ke branch (`git push origin feature/AmazingFeature`)
5. **Open Pull Request**

### Development Guidelines

- Ikuti WordPress Coding Standards
- Tambahkan dokumentasi untuk fungsi baru
- Test semua perubahan sebelum submit
- Gunakan semantic versioning

### Bug Reports

Jika menemukan bug, silakan buat issue dengan informasi:

- WordPress version
- PHP version
- Plugin version
- Tema yang digunakan
- Langkah untuk reproduce bug
- Screenshot jika diperlukan

## 📄 License

Plugin ini dilisensikan under GPL v2 atau yang lebih baru.

## 🆘 Support

Untuk support dan pertanyaan:

- **Issues**: Gunakan GitHub Issues untuk bug reports
- **Documentation**: Baca dokumentasi ini dengan lengkap
- **Community**: Bergabung dengan komunitas WordPress Indonesia

## 📈 Changelog

### Version 1.0.0
- Initial release
- Support untuk WebSite, Business, dan Review schema
- Mode manual dan otomatis
- Auto-fill dengan data Indonesia
- Google Rich Results Test compatibility

---

**Dibuat dengan ❤️ untuk komunitas WordPress Indonesia**