<?php
/**
 * Plugin Name: Rife-SchemaMarkup
 * Plugin URI: https://example.com/rife-schemamarkup
 * Description: WordPress plugin untuk mengelola dan menampilkan Schema Markup JSON-LD dengan kontrol per post/page.
 * Version: 1.0.0
 * Author: Rife Team
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: rife-schemamarkup
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('RIFE_SCHEMA_VERSION', '1.0.0');
define('RIFE_SCHEMA_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('RIFE_SCHEMA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('RIFE_SCHEMA_PLUGIN_FILE', __FILE__);

/**
 * Main plugin class
 */
class RifeSchemaMarkup {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Debug: Plugin init called
        error_log('DEBUG: RifeSchemaMarkup init() called');
        
        // Load text domain
        load_plugin_textdomain('rife-schemamarkup', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Admin hooks
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'admin_init'));
            add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
            add_action('save_post', array($this, 'save_meta_box_data'));
            add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        }
        
        // Frontend hooks - High priority for SEO optimization
        add_action('wp_head', array($this, 'output_schema_markup'), 1);
        add_action('wp_footer', array($this, 'output_schema_markup_footer'), 99);
        
        // Debug: Hooks added
        error_log('DEBUG: wp_head and wp_footer hooks added');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Set default options
        add_option('rife_schema_enabled', '1');
        add_option('rife_schema_output_location', 'head');
        add_option('rife_schema_global_jsonld', '');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('Rife Schema Markup Settings', 'rife-schemamarkup'),
            __('Schema Markup', 'rife-schemamarkup'),
            'manage_options',
            'rife-schemamarkup',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        register_setting('rife_schema_settings', 'rife_schema_enabled');
        register_setting('rife_schema_settings', 'rife_schema_output_location');
        register_setting('rife_schema_settings', 'rife_schema_input_mode');
        register_setting('rife_schema_settings', 'rife_schema_global_jsonld', array(
            'sanitize_callback' => array($this, 'sanitize_json')
        ));
        
        // Auto mode settings
        register_setting('rife_schema_settings', 'rife_schema_auto_website_name');
        register_setting('rife_schema_settings', 'rife_schema_auto_website_url');
        register_setting('rife_schema_settings', 'rife_schema_auto_website_description');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_name');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_type');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_phone');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_email');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_price_range');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_address');
        register_setting('rife_schema_settings', 'rife_schema_auto_business_hours');
        register_setting('rife_schema_settings', 'rife_schema_auto_faq_data');
        register_setting('rife_schema_settings', 'rife_schema_auto_review_data');
        
        add_settings_section(
            'rife_schema_main_section',
            __('Schema Markup Configuration', 'rife-schemamarkup'),
            array($this, 'settings_section_callback'),
            'rife-schemamarkup'
        );
        
        add_settings_field(
            'rife_schema_enabled',
            __('Enable Plugin', 'rife-schemamarkup'),
            array($this, 'enabled_field_callback'),
            'rife-schemamarkup',
            'rife_schema_main_section'
        );
        
        add_settings_field(
            'rife_schema_output_location',
            __('Output Location', 'rife-schemamarkup'),
            array($this, 'output_location_field_callback'),
            'rife-schemamarkup',
            'rife_schema_main_section'
        );
        
        add_settings_field(
            'rife_schema_input_mode',
            __('Input Mode', 'rife-schemamarkup'),
            array($this, 'input_mode_field_callback'),
            'rife-schemamarkup',
            'rife_schema_main_section'
        );
        
        add_settings_field(
            'rife_schema_global_jsonld',
            __('Global JSON-LD', 'rife-schemamarkup'),
            array($this, 'global_jsonld_field_callback'),
            'rife-schemamarkup',
            'rife_schema_main_section'
        );
    }
    
    /**
     * Settings section callback
     */
    public function settings_section_callback() {
        echo '<p>' . __('Configure global Schema Markup settings for your website.', 'rife-schemamarkup') . '</p>';
    }
    
    /**
     * Enabled field callback
     */
    public function enabled_field_callback() {
        $enabled = get_option('rife_schema_enabled', '1');
        echo '<input type="checkbox" id="rife_schema_enabled" name="rife_schema_enabled" value="1" ' . checked('1', $enabled, false) . ' />';
        echo '<label for="rife_schema_enabled">' . __('Enable Schema Markup plugin', 'rife-schemamarkup') . '</label>';
    }
    
    /**
     * Output location field callback
     */
    public function output_location_field_callback() {
        $location = get_option('rife_schema_output_location', 'head');
        echo '<select id="rife_schema_output_location" name="rife_schema_output_location">';
        echo '<option value="head" ' . selected('head', $location, false) . '>' . __('Head Section (Recommended for SEO)', 'rife-schemamarkup') . '</option>';
        echo '<option value="footer" ' . selected('footer', $location, false) . '>' . __('Footer Section', 'rife-schemamarkup') . '</option>';
        echo '</select>';
        echo '<p class="description">' . __('<strong>Recommended:</strong> Head Section - Search engines like Google read code from top to bottom. Placing Schema Markup in the &lt;head&gt; section allows crawlers to immediately find and understand your structured data without scanning the entire &lt;body&gt; content.', 'rife-schemamarkup') . '</p>';
    }
    
    /**
     * Input mode field callback
     */
    public function input_mode_field_callback() {
        $mode = get_option('rife_schema_input_mode', 'manual');
        echo '<div class="rife-schema-mode-radio-group">';
        echo '<label class="rife-radio-option" data-mode="manual">';
        echo '<input type="radio" name="rife_schema_input_mode" value="manual" ' . checked('manual', $mode, false) . ' />';
        echo '<span class="rife-radio-label">' . __('Manual Mode', 'rife-schemamarkup') . '</span>';
        echo '<span class="rife-radio-description">' . __('Enter JSON-LD manually', 'rife-schemamarkup') . '</span>';
        echo '</label>';
        echo '<label class="rife-radio-option" data-mode="auto">';
        echo '<input type="radio" name="rife_schema_input_mode" value="auto" ' . checked('auto', $mode, false) . ' />';
        echo '<span class="rife-radio-label">' . __('Auto Mode', 'rife-schemamarkup') . '</span>';
        echo '<span class="rife-radio-description">' . __('Generate JSON-LD from form', 'rife-schemamarkup') . '</span>';
        echo '</label>';
        echo '</div>';
        echo '<p class="description">' . __('Choose between manual JSON-LD input or auto-generated form.', 'rife-schemamarkup') . '</p>';
    }
    
    /**
     * Global JSON-LD field callback
     */
    public function global_jsonld_field_callback() {
        $value = get_option('rife_schema_global_jsonld', '');
        echo '<textarea id="rife_schema_global_jsonld" name="rife_schema_global_jsonld" rows="15" cols="80" class="large-text code">' . esc_textarea($value) . '</textarea>';
        echo '<p class="description">' . __('Enter your JSON-LD schema markup here. Make sure it\'s valid JSON.', 'rife-schemamarkup') . '</p>';
    }
    
    /**
     * Render auto mode form
     */
    public function render_auto_mode_form() {
        // Get saved values
        $website_name = get_option('rife_schema_auto_website_name', '');
        $website_url = get_option('rife_schema_auto_website_url', '');
        $website_description = get_option('rife_schema_auto_website_description', '');
        $business_name = get_option('rife_schema_auto_business_name', '');
        $business_type = get_option('rife_schema_auto_business_type', 'ProfessionalService');
        $business_phone = get_option('rife_schema_auto_business_phone', '');
        $business_price_range = get_option('rife_schema_auto_business_price_range', '');
        $business_address = get_option('rife_schema_auto_business_address', '');
        $business_hours = get_option('rife_schema_auto_business_hours', '');
        $faq_data = get_option('rife_schema_auto_faq_data', '');
        $review_data = get_option('rife_schema_auto_review_data', '');
        
        echo '<div class="rife-auto-form">';
        
        // Website Information Section
        echo '<h3>' . __('Website Information', 'rife-schemamarkup') . '</h3>';
        echo '<table class="form-table">';
        echo '<tr><th scope="row">' . __('Website Name', 'rife-schemamarkup') . '</th><td>';
        echo '<input type="text" name="rife_schema_auto_website_name" value="' . esc_attr($website_name) . '" class="regular-text" placeholder="' . __('Enter website name', 'rife-schemamarkup') . '" />';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="website_name">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with site name + WordPress', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '<tr><th scope="row">' . __('Website URL', 'rife-schemamarkup') . '</th><td>';
        echo '<input type="url" name="rife_schema_auto_website_url" value="' . esc_attr($website_url) . '" class="regular-text" placeholder="' . __('Enter website URL', 'rife-schemamarkup') . '" />';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="website_url">' . __('Current URL', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with current page URL', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '<tr><th scope="row">' . __('Website Description', 'rife-schemamarkup') . '</th><td>';
        echo '<textarea name="rife_schema_auto_website_description" rows="3" class="large-text" placeholder="' . __('Enter website description', 'rife-schemamarkup') . '">' . esc_textarea($website_description) . '</textarea>';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="website_description">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with site tagline/description', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '</table>';
        
        // Business Information Section
        echo '<h3>' . __('Business Information', 'rife-schemamarkup') . '</h3>';
        echo '<table class="form-table">';
        echo '<tr><th scope="row">' . __('Business Name', 'rife-schemamarkup') . '</th><td>';
        echo '<input type="text" name="rife_schema_auto_business_name" value="' . esc_attr($business_name) . '" class="regular-text" placeholder="' . __('Enter business name', 'rife-schemamarkup') . '" />';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="business_name">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with site name', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '<tr><th scope="row">' . __('Business Type', 'rife-schemamarkup') . '</th><td>';
        echo '<select name="rife_schema_auto_business_type">';
        $business_types = array(
            'ProfessionalService' => __('Professional Service', 'rife-schemamarkup'),
            'LocalBusiness' => __('Local Business', 'rife-schemamarkup'),
            'Organization' => __('Organization', 'rife-schemamarkup'),
            'Corporation' => __('Corporation', 'rife-schemamarkup')
        );
        foreach ($business_types as $value => $label) {
            echo '<option value="' . esc_attr($value) . '"' . selected($business_type, $value, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select></td></tr>';
        echo '<tr><th scope="row">' . __('Phone Number', 'rife-schemamarkup') . '</th><td>';
        echo '<input type="tel" name="rife_schema_auto_business_phone" value="' . esc_attr($business_phone) . '" class="regular-text" placeholder="' . __('Enter business phone', 'rife-schemamarkup') . '" />';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="business_phone">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with admin phone', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '<tr><th scope="row">' . __('Business Email', 'rife-schemamarkup') . '</th><td>';
        echo '<input type="email" name="rife_schema_auto_business_email" value="' . esc_attr(get_option('rife_schema_auto_business_email', '')) . '" class="regular-text" placeholder="' . __('Enter business email', 'rife-schemamarkup') . '" />';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="business_email">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with admin email', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '<tr><th scope="row">' . __('Price Range', 'rife-schemamarkup') . '</th><td><input type="text" name="rife_schema_auto_business_price_range" value="' . esc_attr($business_price_range) . '" class="regular-text" placeholder="e.g., Rp 730.000 - Rp 4.000.000+" /></td></tr>';
        echo '<tr><th scope="row">' . __('Business Address (JSON)', 'rife-schemamarkup') . '</th><td>';
        echo '<textarea name="rife_schema_auto_business_address" rows="4" class="large-text" placeholder=\'{\n  "streetAddress": "Jl. Digital Kreatif No. 123",\n  "addressLocality": "Jakarta",\n  "postalCode": "12345",\n  "addressCountry": "ID"\n}\'>' . esc_textarea($business_address) . '</textarea>';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="business_address">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with site address', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '<tr><th scope="row">' . __('Opening Hours (JSON)', 'rife-schemamarkup') . '</th><td>';
        echo '<textarea name="rife_schema_auto_business_hours" rows="4" class="large-text" placeholder=\'{\n  "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],\n  "opens": "09:00",\n  "closes": "17:00"\n}\'>' . esc_textarea($business_hours) . '</textarea>';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="business_hours">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with default business hours', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '</table>';
        
        // FAQ Section
        echo '<h3>' . __('FAQ Section', 'rife-schemamarkup') . '</h3>';
        echo '<table class="form-table">';
        echo '<tr><th scope="row">' . __('FAQ Data (JSON)', 'rife-schemamarkup') . '</th><td>';
        echo '<textarea name="rife_schema_auto_faq_data" rows="8" class="large-text" placeholder=\'[\n  {\n    "question": "Berapa biaya pembuatan website?",\n    "answer": "Biaya pembuatan website sangat terjangkau..."\n  }\n]\'>' . esc_textarea($faq_data) . '</textarea>';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="faq_data">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with sample FAQ data', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '</table>';
        
        // Review/Rating Section
        echo '<h3>' . __('Review & Rating', 'rife-schemamarkup') . '</h3>';
        echo '<table class="form-table">';
        echo '<tr><th scope="row">' . __('Review Data (JSON)', 'rife-schemamarkup') . '</th><td>';
        echo '<textarea name="rife_schema_auto_review_data" rows="6" class="large-text" placeholder=\'{\n  "ratingValue": "4.9",\n  "bestRating": "5",\n  "worstRating": "1",\n  "ratingCount": "250",\n  "reviewAuthor": "Budi Santoso",\n  "reviewRating": "5",\n  "reviewBody": "Pelayanan luar biasa!"\n}\'>' . esc_textarea($review_data) . '</textarea>';
        echo '<div class="rife-auto-fill-container">';
        echo '<button type="button" class="rife-auto-fill-btn" data-field="review_data">' . __('Auto Fill', 'rife-schemamarkup') . '</button>';
        echo '<span class="rife-auto-fill-hint">' . __('Fill with sample review data', 'rife-schemamarkup') . '</span>';
        echo '</div>';
        echo '</td></tr>';
        echo '</table>';
        
        echo '<div class="auto-mode-actions">';
        echo '<button type="button" id="generate-jsonld" class="button button-secondary">' . __('Generate JSON-LD', 'rife-schemamarkup') . '</button>';
        echo '<button type="button" id="preview-jsonld" class="button button-secondary">' . __('Preview JSON-LD', 'rife-schemamarkup') . '</button>';
        echo '</div>';
        
        echo '<div id="generated-jsonld-preview" style="display: none;">';
        echo '<h4>' . __('Generated JSON-LD:', 'rife-schemamarkup') . '</h4>';
        echo '<textarea id="generated-jsonld-output" rows="15" class="large-text code" readonly></textarea>';
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Generate JSON-LD from auto mode form data for post/page specific
     */
    public function generate_post_specific_jsonld($post_id = null) {
        global $post;
        
        // Use provided post_id or current post
        $current_post = $post_id ? get_post($post_id) : $post;
        
        if (!$current_post) {
            return $this->generate_auto_jsonld();
        }
        
        // Get post/page specific data
        $website_name = $current_post->post_title;
        $website_url = get_permalink($current_post->ID);
        $website_description = '';
        
        // Try to get excerpt first, then content
        if (!empty($current_post->post_excerpt)) {
            $website_description = $current_post->post_excerpt;
        } else {
            // Get first 160 characters of content without HTML tags
            $content = wp_strip_all_tags($current_post->post_content);
            $website_description = wp_trim_words($content, 25, '...');
        }
        
        // Get other data from auto mode settings (business info, etc.)
        $business_name = get_option('rife_schema_auto_business_name', '');
        $business_type = get_option('rife_schema_auto_business_type', 'ProfessionalService');
        $business_phone = get_option('rife_schema_auto_business_phone', '');
        $business_price_range = get_option('rife_schema_auto_business_price_range', '');
        $business_address = get_option('rife_schema_auto_business_address', '');
        $business_hours = get_option('rife_schema_auto_business_hours', '');
        $faq_data = get_option('rife_schema_auto_faq_data', '');
        $review_data = get_option('rife_schema_auto_review_data', '');
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@graph' => array()
        );
        
        // Website Schema - using post/page data
        if (!empty($website_name) && !empty($website_url)) {
            $website_schema = array(
                '@type' => 'WebSite',
                'url' => $website_url,
                'name' => $website_name
            );
            
            // Add search action if URL is provided
            if (!empty($website_url)) {
                $website_schema['potentialAction'] = array(
                    '@type' => 'SearchAction',
                    'target' => rtrim($website_url, '/') . '/?s={search_term_string}',
                    'query-input' => 'required name=search_term_string'
                );
            }
            
            $schema['@graph'][] = $website_schema;
        }
        
        // Business Schema
        if (!empty($business_name)) {
            $business_schema = array(
                '@type' => $business_type,
                'name' => $business_name
            );
            
            if (!empty($website_url)) {
                $business_schema['@id'] = rtrim($website_url, '/') . '/#service';
                $business_schema['url'] = $website_url;
            }
            
            if (!empty($business_phone)) {
                $business_schema['telephone'] = $business_phone;
            }
            
            if (!empty($business_price_range)) {
                $business_schema['priceRange'] = $business_price_range;
            }
            
            if (!empty($website_description)) {
                $business_schema['description'] = $website_description;
            }
            
            // Add placeholder image
            $business_schema['image'] = 'https://placehold.co/600x400/3B82F6/FFFFFF?text=' . urlencode($business_name);
            
            // Parse and add address
            if (!empty($business_address)) {
                $address_data = json_decode($business_address, true);
                if ($address_data) {
                    $business_schema['address'] = array_merge(
                        array('@type' => 'PostalAddress'),
                        $address_data
                    );
                }
            }
            
            // Parse and add opening hours
            if (!empty($business_hours)) {
                $hours_data = json_decode($business_hours, true);
                if ($hours_data) {
                    $business_schema['openingHoursSpecification'] = array_merge(
                        array('@type' => 'OpeningHoursSpecification'),
                        $hours_data
                    );
                }
            }
            
            $schema['@graph'][] = $business_schema;
        }
        
        // FAQ Schema - Disabled (only supported for government/health authoritative sites)
        // Google Rich Results Test only supports FAQPage for well-known, authoritative
        // government-focused or health-focused websites
        /*
        if (!empty($faq_data)) {
            $faq_items = json_decode($faq_data, true);
            if ($faq_items && is_array($faq_items)) {
                $faq_schema = array(
                    '@type' => 'FAQPage',
                    'mainEntity' => array()
                );
                
                foreach ($faq_items as $faq) {
                    if (isset($faq['question']) && isset($faq['answer'])) {
                        $faq_schema['mainEntity'][] = array(
                            '@type' => 'Question',
                            'name' => $faq['question'],
                            'acceptedAnswer' => array(
                                '@type' => 'Answer',
                                'text' => $faq['answer']
                            )
                        );
                    }
                }
                
                if (!empty($faq_schema['mainEntity'])) {
                    $schema['@graph'][] = $faq_schema;
                }
            }
        }
        */
        
        // Review and Rating Schema
        if (!empty($review_data)) {
            $review_info = json_decode($review_data, true);
            if ($review_info) {
                // Aggregate Rating
                if (isset($review_info['ratingValue'])) {
                    $rating_schema = array(
                        '@type' => 'AggregateRating',
                        'itemReviewed' => array(
                            '@type' => $business_type,
                            'name' => !empty($business_name) ? 'Jasa Pembuatan Website oleh ' . $business_name : 'Professional Service'
                        ),
                        'ratingValue' => $review_info['ratingValue'],
                        'bestRating' => isset($review_info['bestRating']) ? $review_info['bestRating'] : '5',
                        'worstRating' => isset($review_info['worstRating']) ? $review_info['worstRating'] : '1',
                        'ratingCount' => isset($review_info['ratingCount']) ? $review_info['ratingCount'] : '1'
                    );
                    
                    $schema['@graph'][] = $rating_schema;
                }
                
                // Individual Review
                if (isset($review_info['reviewAuthor']) && isset($review_info['reviewBody'])) {
                    $review_schema = array(
                        '@type' => 'Review',
                        'itemReviewed' => array(
                            '@type' => $business_type
                        ),
                        'author' => array(
                            '@type' => 'Person',
                            'name' => $review_info['reviewAuthor']
                        ),
                        'reviewRating' => array(
                            '@type' => 'Rating',
                            'ratingValue' => isset($review_info['reviewRating']) ? $review_info['reviewRating'] : '5'
                        ),
                        'reviewBody' => $review_info['reviewBody']
                    );
                    
                    if (!empty($website_url)) {
                        $review_schema['itemReviewed']['@id'] = rtrim($website_url, '/') . '/#service';
                    }
                    
                    $schema['@graph'][] = $review_schema;
                }
            }
        }
        
        return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * Generate JSON-LD from auto mode form data
     */
    public function generate_auto_jsonld() {
        $website_name = get_option('rife_schema_auto_website_name', '');
        $website_url = get_option('rife_schema_auto_website_url', '');
        $website_description = get_option('rife_schema_auto_website_description', '');
        $business_name = get_option('rife_schema_auto_business_name', '');
        $business_type = get_option('rife_schema_auto_business_type', 'ProfessionalService');
        $business_phone = get_option('rife_schema_auto_business_phone', '');
        $business_price_range = get_option('rife_schema_auto_business_price_range', '');
        $business_address = get_option('rife_schema_auto_business_address', '');
        $business_hours = get_option('rife_schema_auto_business_hours', '');
        $faq_data = get_option('rife_schema_auto_faq_data', '');
        $review_data = get_option('rife_schema_auto_review_data', '');
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@graph' => array()
        );
        
        // Website Schema
        if (!empty($website_name) && !empty($website_url)) {
            $website_schema = array(
                '@type' => 'WebSite',
                'url' => $website_url,
                'name' => $website_name
            );
            
            // Add search action if URL is provided
            if (!empty($website_url)) {
                $website_schema['potentialAction'] = array(
                    '@type' => 'SearchAction',
                    'target' => rtrim($website_url, '/') . '/?s={search_term_string}',
                    'query-input' => 'required name=search_term_string'
                );
            }
            
            $schema['@graph'][] = $website_schema;
        }
        
        // Business Schema
        if (!empty($business_name)) {
            $business_schema = array(
                '@type' => $business_type,
                'name' => $business_name
            );
            
            if (!empty($website_url)) {
                $business_schema['@id'] = rtrim($website_url, '/') . '/#service';
                $business_schema['url'] = $website_url;
            }
            
            if (!empty($business_phone)) {
                $business_schema['telephone'] = $business_phone;
            }
            
            if (!empty($business_price_range)) {
                $business_schema['priceRange'] = $business_price_range;
            }
            
            if (!empty($website_description)) {
                $business_schema['description'] = $website_description;
            }
            
            // Add placeholder image
            $business_schema['image'] = 'https://placehold.co/600x400/3B82F6/FFFFFF?text=' . urlencode($business_name);
            
            // Parse and add address
            if (!empty($business_address)) {
                $address_data = json_decode($business_address, true);
                if ($address_data) {
                    $business_schema['address'] = array_merge(
                        array('@type' => 'PostalAddress'),
                        $address_data
                    );
                }
            }
            
            // Parse and add opening hours
            if (!empty($business_hours)) {
                $hours_data = json_decode($business_hours, true);
                if ($hours_data) {
                    $business_schema['openingHoursSpecification'] = array_merge(
                        array('@type' => 'OpeningHoursSpecification'),
                        $hours_data
                    );
                }
            }
            
            $schema['@graph'][] = $business_schema;
        }
        
        // FAQ Schema - Disabled (only supported for government/health authoritative sites)
        // Google Rich Results Test only supports FAQPage for well-known, authoritative
        // government-focused or health-focused websites
        /*
        if (!empty($faq_data)) {
            $faq_items = json_decode($faq_data, true);
            if ($faq_items && is_array($faq_items)) {
                $faq_schema = array(
                    '@type' => 'FAQPage',
                    'mainEntity' => array()
                );
                
                foreach ($faq_items as $faq) {
                    if (isset($faq['question']) && isset($faq['answer'])) {
                        $faq_schema['mainEntity'][] = array(
                            '@type' => 'Question',
                            'name' => $faq['question'],
                            'acceptedAnswer' => array(
                                '@type' => 'Answer',
                                'text' => $faq['answer']
                            )
                        );
                    }
                }
                
                if (!empty($faq_schema['mainEntity'])) {
                    $schema['@graph'][] = $faq_schema;
                }
            }
        }
        */
        
        // Review and Rating Schema
        if (!empty($review_data)) {
            $review_info = json_decode($review_data, true);
            if ($review_info) {
                // Aggregate Rating
                if (isset($review_info['ratingValue'])) {
                    $rating_schema = array(
                        '@type' => 'AggregateRating',
                        'itemReviewed' => array(
                            '@type' => $business_type,
                            'name' => !empty($business_name) ? 'Jasa Pembuatan Website oleh ' . $business_name : 'Professional Service'
                        ),
                        'ratingValue' => $review_info['ratingValue'],
                        'bestRating' => isset($review_info['bestRating']) ? $review_info['bestRating'] : '5',
                        'worstRating' => isset($review_info['worstRating']) ? $review_info['worstRating'] : '1',
                        'ratingCount' => isset($review_info['ratingCount']) ? $review_info['ratingCount'] : '1'
                    );
                    
                    $schema['@graph'][] = $rating_schema;
                }
                
                // Individual Review
                if (isset($review_info['reviewAuthor']) && isset($review_info['reviewBody'])) {
                    $review_schema = array(
                        '@type' => 'Review',
                        'itemReviewed' => array(
                            '@type' => $business_type
                        ),
                        'author' => array(
                            '@type' => 'Person',
                            'name' => $review_info['reviewAuthor']
                        ),
                        'reviewRating' => array(
                            '@type' => 'Rating',
                            'ratingValue' => isset($review_info['reviewRating']) ? $review_info['reviewRating'] : '5'
                        ),
                        'reviewBody' => $review_info['reviewBody']
                    );
                    
                    if (!empty($website_url)) {
                        $review_schema['itemReviewed']['@id'] = rtrim($website_url, '/') . '/#service';
                    }
                    
                    $schema['@graph'][] = $review_schema;
                }
            }
        }
        
        return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * Sanitize JSON input
     */
    public function sanitize_json($input) {
        if (empty($input)) {
            return '';
        }
        
        // Validate JSON
        $decoded = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            add_settings_error(
                'rife_schema_global_jsonld',
                'invalid_json',
                __('Invalid JSON format. Please check your JSON-LD syntax.', 'rife-schemamarkup')
            );
            return get_option('rife_schema_global_jsonld', '');
        }
        
        return $input;
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        $current_mode = get_option('rife_schema_input_mode', 'manual');
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('rife_schema_settings');
                
                // Output Location Field
                echo '<table class="form-table"><tbody>';
                echo '<tr><th scope="row">Output Location</th><td>';
                $this->output_location_field_callback();
                echo '</td></tr>';
                
                // Input Mode Toggle
                echo '<tr><th scope="row">Input Mode</th><td>';
                $this->input_mode_field_callback();
                echo '</td></tr>';
                
                // Manual Mode Content
                echo '<tr id="manual-mode-row" style="display: ' . ($current_mode === 'manual' ? 'table-row' : 'none') . ';"><th scope="row">Global JSON-LD Schema</th><td>';
                $this->global_jsonld_field_callback();
                echo '</td></tr>';
                
                echo '</tbody></table>';
                
                // Auto Mode Content
                echo '<div id="auto-mode-content" style="display: ' . ($current_mode === 'auto' ? 'block' : 'none') . ';">';
                echo '<h2>Auto Mode - Schema Generator</h2>';
                $this->render_auto_mode_form();
                echo '</div>';
                
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        $post_types = array('post', 'page');
        foreach ($post_types as $post_type) {
            add_meta_box(
                'rife-schema-meta-box',
                __('Schema Markup', 'rife-schemamarkup'),
                array($this, 'meta_box_callback'),
                $post_type,
                'normal',
                'default'
            );
        }
    }
    
    /**
     * Meta box callback
     */
    public function meta_box_callback($post) {
        wp_nonce_field('rife_schema_meta_box', 'rife_schema_meta_box_nonce');
        
        $enabled = get_post_meta($post->ID, '_rife_schema_enabled', true);
        $custom_jsonld = get_post_meta($post->ID, '_rife_schema_custom_jsonld', true);
        $status = get_post_meta($post->ID, '_rife_schema_status', true);
        
        // Set default to enabled for new posts/pages
        if (empty($enabled) && empty($status)) {
            $enabled = '1';
            $status = 'enabled';
        } elseif (empty($status)) {
            $status = $enabled ? 'enabled' : 'disabled';
        }
        
        // Get current input mode and auto-generated JSON-LD
        $input_mode = get_option('rife_schema_input_mode', 'manual');
        $auto_jsonld = '';
        if ($input_mode === 'auto') {
            $auto_jsonld = $this->generate_post_specific_jsonld($post->ID);
        }
        
        ?>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Enable Schema Markup', 'rife-schemamarkup'); ?></th>
                <td>
                    <label class="rife-schema-toggle">
                        <input type="checkbox" id="rife_schema_enabled" name="rife_schema_enabled" value="1" <?php checked('1', $enabled); ?> />
                        <span class="rife-schema-slider"></span>
                    </label>
                    <p class="description"><?php _e('Enable Schema Markup for this post/page (Default: Enabled)', 'rife-schemamarkup'); ?></p>
                    <div id="schema-status" class="schema-status-<?php echo esc_attr($status); ?>">
                        <strong><?php _e('Status:', 'rife-schemamarkup'); ?></strong> 
                        <span id="status-text"><?php echo $enabled ? __('Enabled', 'rife-schemamarkup') : __('Disabled', 'rife-schemamarkup'); ?></span>
                    </div>
                </td>
            </tr>
            <?php if ($input_mode === 'auto' && !empty($auto_jsonld)) : ?>
            <tr id="auto-mode-info-row" style="<?php echo $enabled ? '' : 'display: none;'; ?>">
                <th scope="row"><?php _e('Auto Mode Status', 'rife-schemamarkup'); ?></th>
                <td>
                    <div class="auto-mode-status">
                        <div class="auto-mode-indicator">
                            <span class="dashicons dashicons-yes-alt" style="color: #00a32a; font-size: 20px; vertical-align: middle;"></span>
                            <strong style="color: #00a32a; vertical-align: middle;"><?php _e('Schema Markup is automatically active!', 'rife-schemamarkup'); ?></strong>
                        </div>
                        <p class="description" style="margin-top: 10px;">
                            <?php _e('Schema Markup is automatically generated based on this post/page data. No manual input required - just keep this toggle enabled.', 'rife-schemamarkup'); ?>
                        </p>
                        <details style="margin-top: 15px;">
                            <summary style="cursor: pointer; font-weight: bold;"><?php _e('View Generated Schema (Optional)', 'rife-schemamarkup'); ?></summary>
                            <div style="margin-top: 10px;">
                                <textarea readonly rows="6" cols="80" class="large-text code auto-generated-schema" style="background: #f9f9f9; border: 1px solid #ddd;"><?php echo esc_textarea($auto_jsonld); ?></textarea>
                                <p class="description"><?php _e('This schema is automatically applied to your page. You don\'t need to copy or modify it.', 'rife-schemamarkup'); ?></p>
                            </div>
                        </details>
                    </div>
                </td>
            </tr>
            <?php elseif ($input_mode === 'manual') : ?>
            <tr id="custom-jsonld-row" style="<?php echo $enabled ? '' : 'display: none;'; ?>">
                <th scope="row"><?php _e('Custom JSON-LD', 'rife-schemamarkup'); ?></th>
                <td>
                    <textarea id="rife_schema_custom_jsonld" name="rife_schema_custom_jsonld" rows="8" cols="80" class="large-text code"><?php echo esc_textarea($custom_jsonld); ?></textarea>
                    <p class="description"><?php _e('Add custom JSON-LD for this specific post/page. This will be merged with global JSON-LD.', 'rife-schemamarkup'); ?></p>
                    <div id="custom-json-validation-result"></div>
                </td>
            </tr>
            <?php endif; ?>
            <?php if ($input_mode === 'manual') : ?>
            <tr id="preview-row" style="<?php echo $enabled ? '' : 'display: none;'; ?>">
                <th scope="row"><?php _e('Schema Preview', 'rife-schemamarkup'); ?></th>
                <td>
                    <div id="schema-preview" class="schema-preview">
                        <h4><?php _e('Combined Schema Output:', 'rife-schemamarkup'); ?></h4>
                        <pre id="preview-content"></pre>
                    </div>
                </td>
            </tr>
            <?php endif; ?>
        </table>
        <?php
    }
    
    /**
     * Save meta box data
     */
    public function save_meta_box_data($post_id) {
        if (!isset($_POST['rife_schema_meta_box_nonce']) || !wp_verify_nonce($_POST['rife_schema_meta_box_nonce'], 'rife_schema_meta_box')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        $enabled = isset($_POST['rife_schema_enabled']) ? '1' : '0';
        $status = $enabled ? 'enabled' : 'disabled';
        
        // Get current input mode
        $input_mode = get_option('rife_schema_input_mode', 'manual');
        
        // Only save custom JSON-LD in manual mode
        if ($input_mode === 'manual') {
            $custom_jsonld = isset($_POST['rife_schema_custom_jsonld']) ? sanitize_textarea_field($_POST['rife_schema_custom_jsonld']) : '';
            update_post_meta($post_id, '_rife_schema_custom_jsonld', $custom_jsonld);
        } else {
            // In auto mode, clear any existing custom JSON-LD
            delete_post_meta($post_id, '_rife_schema_custom_jsonld');
        }
        
        update_post_meta($post_id, '_rife_schema_enabled', $enabled);
        update_post_meta($post_id, '_rife_schema_status', $status);
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if ($hook === 'settings_page_rife-schemamarkup' || $hook === 'post.php' || $hook === 'post-new.php') {
            wp_enqueue_script(
                'rife-schema-admin',
                RIFE_SCHEMA_PLUGIN_URL . 'admin.js',
                array('jquery'),
                RIFE_SCHEMA_VERSION,
                true
            );
            
            wp_enqueue_style(
                'rife-schema-admin',
                RIFE_SCHEMA_PLUGIN_URL . 'admin.css',
                array(),
                RIFE_SCHEMA_VERSION
            );
            
            wp_localize_script('rife-schema-admin', 'rifeSchemaAjax', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('rife_schema_nonce'),
                'strings' => array(
                    'enabled' => __('Enabled', 'rife-schemamarkup'),
                    'disabled' => __('Disabled', 'rife-schemamarkup'),
                    'validJson' => __('Valid JSON', 'rife-schemamarkup'),
                    'invalidJson' => __('Invalid JSON', 'rife-schemamarkup')
                )
            ));
        }
    }
    
    /**
     * Output schema markup on frontend (HEAD section)
     * Priority 1 ensures early loading for SEO optimization
     */
    public function output_schema_markup() {
        // Debug: Method called
        error_log('DEBUG: output_schema_markup() called');
        echo "<!-- DEBUG: output_schema_markup() called -->\n";
        
        // Only output in head if location is set to head
        $output_location = get_option('rife_schema_output_location', 'head');
        error_log('DEBUG: output_location = ' . $output_location);
        echo "<!-- DEBUG: output_location = {$output_location} -->\n";
        
        if ($output_location !== 'head') {
            error_log('DEBUG: Not head location, returning');
            echo "<!-- DEBUG: Not head location, returning -->\n";
            return;
        }
        
        error_log('DEBUG: Calling render_schema_markup()');
        echo "<!-- DEBUG: Calling render_schema_markup() -->\n";
        $this->render_schema_markup();
    }
    
    /**
     * Output schema markup on frontend (FOOTER section)
     * Fallback option for footer placement
     */
    public function output_schema_markup_footer() {
        // Only output in footer if location is set to footer
        $output_location = get_option('rife_schema_output_location', 'head');
        if ($output_location !== 'footer') {
            return;
        }
        
        $this->render_schema_markup();
    }
    
    /**
     * Render schema markup - Core logic for both head and footer
     */
    private function render_schema_markup() {
        // Debug: Always output something to verify plugin is running
        echo "\n<!-- DEBUG: Rife Schema Plugin is running -->\n";
        
        // Check if plugin is enabled
        if (!get_option('rife_schema_enabled', '1')) {
            echo "\n<!-- DEBUG: Plugin is disabled -->\n";
            return;
        }
        
        echo "\n<!-- DEBUG: Plugin is enabled -->\n";
        echo "\n<!-- DEBUG: is_single() = " . (is_single() ? 'true' : 'false') . " -->\n";
        echo "\n<!-- DEBUG: is_page() = " . (is_page() ? 'true' : 'false') . " -->\n";
        
        global $post;
        echo "\n<!-- DEBUG: Post ID = " . (isset($post) ? $post->ID : 'no post') . " -->\n";
        
        // Check if we're on a single post/page
        if (!is_singular() || !$post) {
            return;
        }
        
        // Check if schema is enabled for this post
        $post_enabled = get_post_meta($post->ID, '_rife_schema_enabled', true);
        
        // Set default to enabled for new posts/pages
        if (empty($post_enabled)) {
            $post_enabled = '1';
        }
        
        if (!$post_enabled) {
            return;
        }
        
        // Check input mode and get appropriate JSON-LD
        $input_mode = get_option('rife_schema_input_mode', 'manual');
        $output_schemas = array();
        
        if ($input_mode === 'auto') {
            // In auto mode, only use post-specific auto-generated schema
            $auto_jsonld = $this->generate_post_specific_jsonld($post->ID);
            if (!empty($auto_jsonld)) {
                $decoded_auto = json_decode($auto_jsonld, true);
                if (json_last_error() === JSON_ERROR_NONE && $decoded_auto) {
                    $output_schemas[] = $decoded_auto;
                }
            }
        } else {
            // In manual mode, use global + custom JSON-LD
            $global_jsonld = get_option('rife_schema_global_jsonld', '');
            $custom_jsonld = get_post_meta($post->ID, '_rife_schema_custom_jsonld', true);
            
            // Add global JSON-LD if exists
            if (!empty($global_jsonld)) {
                $decoded_global = json_decode($global_jsonld, true);
                if (json_last_error() === JSON_ERROR_NONE && $decoded_global) {
                    $output_schemas[] = $decoded_global;
                }
            }
            
            // Add custom JSON-LD if exists
            if (!empty($custom_jsonld)) {
                $decoded_custom = json_decode($custom_jsonld, true);
                if (json_last_error() === JSON_ERROR_NONE && $decoded_custom) {
                    $output_schemas[] = $decoded_custom;
                }
            }
        }
        
        // Output schemas with SEO-optimized format
        if (!empty($output_schemas)) {
            $output_location = get_option('rife_schema_output_location', 'head');
            $location_comment = ($output_location === 'head') ? 'HEAD' : 'FOOTER';
            
            echo "\n<!-- Rife Schema Markup ({$location_comment}) - Optimized for Google Crawlers -->\n";
            foreach ($output_schemas as $schema) {
                echo '<script type="application/ld+json">';
                echo wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
                echo '</script>' . "\n";
            }
            echo "<!-- /Rife Schema Markup ({$location_comment}) -->\n";
        }
    }
}

// Initialize the plugin
$rife_schema_plugin = new RifeSchemaMarkup();
$rife_schema_plugin->init();